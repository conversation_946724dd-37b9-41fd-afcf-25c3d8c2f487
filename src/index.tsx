import './assets/css/index.css';
import './assets/css/table-fix.css';
import './assets/css/floating-labels.css';
import './styles/global.scss';

import { ConfigProvider } from 'antd';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ToastContainer } from 'react-toastify';

import ErrorBoundary from './components/ErrorBoundary';
import RouteApp from './containers/AdminPage/RouteApp';
import Auth from './containers/Auth';
import reportWebVitals from './reportWebVitals';

import { Providers, customTheme } from '@/utils/ui';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <Providers>
    <ConfigProvider
      theme={customTheme}
      componentSize="middle"
      table={{
        className: 'global-bordered-table'
      }}
    >
      <ErrorBoundary>
        <Auth>
          <RouteApp />
        </Auth>
      </ErrorBoundary>
    </ConfigProvider>
    <ToastContainer />
  </Providers>,
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
